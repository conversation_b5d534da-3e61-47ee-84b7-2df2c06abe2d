// src/main.rs
use mmkvreader::Mmkv;
use std::path::Path;
use clap::{Parser, Subcommand, ValueEnum};

#[derive(Parser)]
#[command(name = "mmkvreader")]
#[command(about = "MMKV 批量搜索工具")]
#[command(version = "1.0")]
struct Args {
    #[command(subcommand)]
    command: Option<Commands>,

    /// tgz 文件所在目录
    #[arg(value_name = "DIRECTORY")]
    directory: Option<String>,

    /// 加密密钥 (可选)
    #[arg(short = 'k', long = "key", default_value = "fd9f5bef68c54a1ecf70757a6d6f565b")]
    crypt_key: String,

    /// 搜索关键字 (支持多个关键字，分隔符: 中文逗号、英文逗号、空格、竖杠)
    #[arg(short = 's', long = "search")]
    search_terms: Option<String>,

    /// 搜索模式 (默认按key搜索)
    #[arg(short = 'm', long = "mode", default_value = "key")]
    search_mode: SearchModeArg,

    /// 是否压缩结果到 zip 文件
    #[arg(short = 'z', long = "zip", default_value = "yes")]
    compress: CompressArg,

    /// 线程数 (默认CPU数-1，最大1024)
    #[arg(short = 't', long = "threads")]
    threads: Option<usize>,

    /// 是否清理临时文件 (解压目录和JSON文件)
    #[arg(short = 'c', long = "cleanup", default_value = "yes")]
    cleanup: CleanupArg,

    /// 启用调试模式，显示详细的调试信息
    #[arg(long = "debug",  default_value = "false")]
    debug: bool,
}

#[derive(Subcommand)]
enum Commands {
    /// 清理临时文件 (解压目录和JSON文件)
    Clean {
        /// 要清理的目录
        directory: String,
    },
    /// 压缩所有JSON文件为ZIP文件
    Zip {
        /// 包含JSON文件的目录
        directory: String,
    },
    /// 测试MMKV文件加载和读取
    Test,
}

#[derive(Clone, Debug, ValueEnum)]
enum SearchModeArg {
    /// 在 key 中搜索
    Key,
    /// 在 value 中搜索
    Value,
}

#[derive(Clone, Debug, ValueEnum)]
enum CompressArg {
    /// 压缩结果
    Yes,
    /// 不压缩结果
    No,
}

#[derive(Clone, Debug, ValueEnum)]
enum CleanupArg {
    /// 清理临时文件
    Yes,
    /// 不清理临时文件
    No,
}

fn main() {
    let args = Args::parse();

    // 处理子命令
    match args.command {
        Some(Commands::Clean { directory }) => {
            run_clean_command(&directory);
            return;
        }
        Some(Commands::Zip { directory }) => {
            run_zip_command(&directory);
            return;
        }
        Some(Commands::Test) => {
            mmkvreader::test_mmkv::test_mmkv_with_params();
            return;
        }
        None => {
            // 继续执行搜索命令
        }
    }

    // 搜索命令需要的参数验证
    let input_path = args.directory.as_ref().expect("路径参数是必需的");

    // 解析搜索关键字 - 如果为空则返回空数组
    let search_keywords = if let Some(search_terms) = args.search_terms.as_ref() {
        parse_search_terms(search_terms)
    } else {
        Vec::new() // 空关键字表示返回所有内容
    };

    // 转换搜索模式
    let search_mode = match args.search_mode {
        SearchModeArg::Key => 1,
        SearchModeArg::Value => 2,
    };

    // 转换压缩选项
    let should_compress = matches!(args.compress, CompressArg::Yes);

    // 转换清理选项
    let should_cleanup = matches!(args.cleanup, CleanupArg::Yes);

    // 计算默认线程数：CPU数-1，最小为1
    let default_threads = std::cmp::max(1, num_cpus::get().saturating_sub(1));

    // 验证线程数
    let threads = args.threads.unwrap_or(default_threads);
    let threads = if threads == 0 {
        default_threads
    } else if threads > 1024 {
        1024
    } else {
        threads
    };

    // 检查路径是否存在
    let path = Path::new(input_path);
    if !path.exists() {
        eprintln!("❌ 错误: 路径不存在: {}", input_path);
        std::process::exit(1);
    }

    // 检测输入路径类型并相应处理
    if path.is_file() && input_path.ends_with(".tgz") {
        // 处理单个tgz文件
        println!("🔍 MMKV 单文件搜索工具");
        println!("{}", "=".repeat(50));
        println!("� 文件: {}", input_path);
        println!("🔑 加密密钥: {}", args.crypt_key);
        if search_keywords.is_empty() {
            println!("🔍 搜索关键字: <空> (返回所有key和value)");
        } else {
            println!("🔍 搜索关键字: {:?}", search_keywords);
        }
        println!("📋 搜索模式: {:?}", args.search_mode);
        println!("📦 压缩结果: {}", if should_compress { "是" } else { "否" });
        println!("🧹 清理临时文件: {}", if should_cleanup { "是" } else { "否" });
        println!("🐛 调试模式: {}", if args.debug { "启用" } else { "关闭" });
        println!();

        // 执行单文件搜索
        run_single_file_search(input_path, &args.crypt_key, &search_keywords, search_mode, should_compress, should_cleanup, args.debug);
    } else if path.is_dir() {
        // 处理目录中的所有tgz文件
        println!("🔍 MMKV 批量搜索工具");
        println!("{}", "=".repeat(50));
        println!("📁 目录: {}", input_path);
        println!("🔑 加密密钥: {}", args.crypt_key);
        if search_keywords.is_empty() {
            println!("🔍 搜索关键字: <空> (返回所有key和value)");
        } else {
            println!("🔍 搜索关键字: {:?}", search_keywords);
        }
        println!("📋 搜索模式: {:?}", args.search_mode);
        println!("📦 压缩结果: {}", if should_compress { "是" } else { "否" });
        println!("🧵 线程数: {} (CPU数: {})", threads, num_cpus::get());
        println!("🧹 清理临时文件: {}", if should_cleanup { "是" } else { "否" });
        println!("🐛 调试模式: {}", if args.debug { "启用" } else { "关闭" });
        println!();

        // 执行批量搜索
        run_batch_search(input_path, &args.crypt_key, &search_keywords, search_mode, should_compress, threads, should_cleanup, None, args.debug);
    } else {
        eprintln!("❌ 错误: 输入路径必须是目录或.tgz文件: {}", input_path);
        std::process::exit(1);
    }
}

/// 解析搜索关键字，支持多种分隔符
fn parse_search_terms(input: &str) -> Vec<String> {
    // 支持的分隔符：中文逗号、英文逗号、空格、竖杠
    let separators = ['，', ',', ' ', '|'];

    let mut terms = Vec::new();
    let mut current_term = String::new();

    for ch in input.chars() {
        if separators.contains(&ch) {
            if !current_term.trim().is_empty() {
                terms.push(current_term.trim().to_string());
                current_term.clear();
            }
        } else {
            current_term.push(ch);
        }
    }

    // 添加最后一个关键字
    if !current_term.trim().is_empty() {
        terms.push(current_term.trim().to_string());
    }

    // 去重并过滤空字符串
    let mut unique_terms: Vec<String> = terms.into_iter()
        .filter(|s| !s.is_empty())
        .collect::<std::collections::HashSet<_>>()
        .into_iter()
        .collect();

    unique_terms.sort();
    unique_terms
}

/// 清理命令：清理临时文件
fn run_clean_command(directory: &str) {
    println!("🧹 开始清理临时文件");
    println!("📁 目录: {}", directory);

    let mut cleaned_dirs = 0;
    let mut cleaned_files = 0;
    let mut errors = 0;

    // 首先收集所有tgz文件名（不包含扩展名）
    let mut tgz_basenames = std::collections::HashSet::new();

    match std::fs::read_dir(directory) {
        Ok(entries) => {
            for entry in entries {
                if let Ok(entry) = entry {
                    let path = entry.path();
                    let file_name = entry.file_name();
                    let file_name_str = file_name.to_string_lossy();

                    // 收集tgz文件的基础名称（不包含.tgz扩展名）
                    if path.is_file() && file_name_str.ends_with(".tgz") {
                        if let Some(basename) = file_name_str.strip_suffix(".tgz") {
                            tgz_basenames.insert(basename.to_string());
                        }
                    }
                }
            }
        }
        Err(e) => {
            eprintln!("❌ 无法读取目录 {}: {}", directory, e);
            return;
        }
    }

    println!("📦 找到 {} 个 tgz 文件", tgz_basenames.len());

    // 然后清理与tgz文件同名的目录和JSON文件
    match std::fs::read_dir(directory) {
        Ok(entries) => {
            for entry in entries {
                if let Ok(entry) = entry {
                    let path = entry.path();
                    let file_name = entry.file_name();
                    let file_name_str = file_name.to_string_lossy();

                    // 清理与tgz文件同名的解压目录
                    if path.is_dir() && tgz_basenames.contains(file_name_str.as_ref()) {
                        match std::fs::remove_dir_all(&path) {
                            Ok(_) => {
                                cleaned_dirs += 1;
                                println!("🗑️  删除目录: {}", file_name_str);
                            }
                            Err(e) => {
                                errors += 1;
                                eprintln!("❌ 删除目录失败 {}: {}", file_name_str, e);
                            }
                        }
                    }

                    // 清理JSON文件（但保留ck_search_results_开头的ZIP文件）
                    if path.is_file() && file_name_str.ends_with(".json") && !file_name_str.starts_with("ck_search_results_") {
                        match std::fs::remove_file(&path) {
                            Ok(_) => {
                                cleaned_files += 1;
                                println!("🗑️  删除文件: {}", file_name_str);
                            }
                            Err(e) => {
                                errors += 1;
                                eprintln!("❌ 删除文件失败 {}: {}", file_name_str, e);
                            }
                        }
                    }
                }
            }
        }
        Err(e) => {
            eprintln!("❌ 无法读取目录 {}: {}", directory, e);
            return;
        }
    }

    println!("\n✅ 清理完成!");
    println!("📊 统计:");
    println!("   删除目录: {} 个", cleaned_dirs);
    println!("   删除文件: {} 个", cleaned_files);
    if errors > 0 {
        println!("   错误: {} 个", errors);
    }
}

/// ZIP命令：压缩所有JSON文件
fn run_zip_command(directory: &str) {
    println!("📦 开始压缩JSON文件");
    println!("📁 目录: {}", directory);

    // 收集所有JSON文件
    let mut json_files = Vec::new();

    match std::fs::read_dir(directory) {
        Ok(entries) => {
            for entry in entries {
                if let Ok(entry) = entry {
                    let path = entry.path();
                    let file_name = entry.file_name();
                    let file_name_str = file_name.to_string_lossy();

                    // 收集JSON文件（排除已有的ZIP文件）
                    if path.is_file() && file_name_str.ends_with(".json") && !file_name_str.starts_with("ck_search_results_") {
                        json_files.push(path.to_string_lossy().to_string());
                    }
                }
            }
        }
        Err(e) => {
            eprintln!("❌ 无法读取目录 {}: {}", directory, e);
            return;
        }
    }

    if json_files.is_empty() {
        println!("📄 没有找到JSON文件");
        return;
    }

    println!("📄 找到 {} 个JSON文件", json_files.len());

    // 创建ZIP文件
    match create_zip_archive(directory, &json_files, &["manual_zip".to_string()]) {
        Ok(zip_path) => {
            println!("✅ 压缩完成: {}", zip_path);

            // 询问是否删除原始JSON文件
            println!("🤔 是否删除原始JSON文件? (y/N)");
            let mut input = String::new();
            if std::io::stdin().read_line(&mut input).is_ok() {
                let input = input.trim().to_lowercase();
                if input == "y" || input == "yes" {
                    let mut deleted = 0;
                    for file_path in &json_files {
                        if std::fs::remove_file(file_path).is_ok() {
                            deleted += 1;
                        }
                    }
                    println!("🗑️  已删除 {} 个JSON文件", deleted);
                }
            }
        }
        Err(e) => {
            eprintln!("❌ 压缩失败: {}", e);
        }
    }
}

/// 执行单文件搜索
fn run_single_file_search(
    tgz_file_path: &str,
    crypt_key: &str,
    search_keywords: &[String],
    search_mode: i32,
    should_compress: bool,
    should_cleanup: bool,
    debug: bool,
) {
    let start_time = std::time::Instant::now();

    if search_keywords.is_empty() {
        println!("🚀 使用全量导出模式");
        println!("📋 导出所有key和value");
    } else {
        println!("🚀 使用单文件搜索模式");
        println!("🔍 关键字列表: {:?}", search_keywords);
    }

    let tgz_path = Path::new(tgz_file_path);

    // 使用单文件处理方法
    match Mmkv::process_single_tgz_file_multi_keywords(
        tgz_path,
        Some(crypt_key),
        search_keywords,
        search_mode.into(),
        should_cleanup,
        None, // search_dir
        debug,
    ) {
        Ok(result) => {
            if search_keywords.is_empty() {
                println!("✅ 单文件全量导出完成: 导出 {} 条记录",
                         result.total_matches);
            } else {
                println!("✅ 单文件搜索完成: 找到 {} 匹配",
                         result.total_matches);
            }

            let total_time = start_time.elapsed();

            println!("\n{}", "=".repeat(50));
            if search_keywords.is_empty() {
                println!("🎉 单文件全量导出任务完成!");
                println!("📊 统计:");
                println!("   导出模式: 全量导出");
                println!("   总记录数: {}", result.total_matches);
                println!("   处理的MMKV文件数: {}", result.total_mmkv_files);
                println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());
            } else {
                println!("🎉 单文件搜索任务完成!");
                println!("📊 统计:");
                println!("   搜索关键字: {} 个", search_keywords.len());
                println!("   总匹配数: {}", result.total_matches);
                println!("   处理的MMKV文件数: {}", result.total_mmkv_files);
                println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());
            }

            // 获取生成的JSON文件路径
            let tgz_name = tgz_path.file_stem()
                .and_then(|name| name.to_str())
                .unwrap_or("unknown");

            let json_path = tgz_path.parent()
                .unwrap_or_else(|| Path::new("."))
                .join(format!("{}.json", tgz_name));

            if json_path.exists() {
                if should_compress {
                    // 压缩单个JSON文件
                    let directory = tgz_path.parent()
                        .unwrap_or_else(|| Path::new("."))
                        .to_string_lossy();

                    match create_zip_archive(&directory, &[json_path.to_string_lossy().to_string()], search_keywords) {
                        Ok(zip_path) => {
                            println!("📦 结果已压缩到: {}", zip_path);

                            // 删除原始JSON文件
                            let _ = std::fs::remove_file(&json_path);
                            println!("🧹 已清理临时JSON文件");
                        }
                        Err(e) => {
                            eprintln!("❌ 创建ZIP文件失败: {}", e);
                        }
                    }
                } else {
                    println!("📄 生成了JSON结果文件: {}", json_path.display());
                }
            }
        }
        Err(e) => {
            eprintln!("❌ 单文件搜索失败: {}", e);
        }
    }
}

/// 执行批量搜索 - 优化版：一次打开文件搜索所有关键字
fn run_batch_search(
    directory: &str,
    crypt_key: &str,
    search_keywords: &[String],
    search_mode: i32,
    should_compress: bool,
    threads: usize,
    should_cleanup: bool,
    search_dir: Option<&str>,
    debug: bool,
) {
    let start_time = std::time::Instant::now();

    if search_keywords.is_empty() {
        println!("🚀 使用全量导出模式");
        println!("📋 导出所有key和value");
    } else {
        println!("🚀 使用优化的多关键字搜索模式");
        println!("🔍 关键字列表: {:?}", search_keywords);
    }

    // 使用新的多关键字搜索方法
    match Mmkv::search_in_tgz_directory_multi_keywords(
        directory,
        Some(crypt_key),
        search_keywords,
        search_mode,
        threads,
        should_cleanup,
        search_dir,
        debug,
    ) {
        Ok(stats) => {
            if search_keywords.is_empty() {
                println!("✅ 全量导出完成: 处理 {} 文件，导出 {} 条记录",
                         stats.processed_files, stats.total_matches);
            } else {
                println!("✅ 多关键字搜索完成: 处理 {} 文件，找到 {} 匹配",
                         stats.processed_files, stats.total_matches);
            }

            // 收集结果文件
            let all_results = collect_multi_keyword_result_files(directory, search_keywords)
                .unwrap_or_else(|e| {
                    eprintln!("❌ 收集结果文件失败: {}", e);
                    Vec::new()
                });

            let total_time = start_time.elapsed();

            println!("\n{}", "=".repeat(50));
            if search_keywords.is_empty() {
                println!("🎉 全量导出任务完成!");
                println!("📊 总体统计:");
                println!("   导出模式: 全量导出");
                println!("   总记录数: {}", stats.total_matches);
                println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());
            } else {
                println!("🎉 多关键字搜索任务完成!");
                println!("📊 总体统计:");
                println!("   搜索关键字: {} 个", search_keywords.len());
                println!("   总匹配数: {}", stats.total_matches);
                println!("   总耗时: {:.2} 秒", total_time.as_secs_f64());
            }
            // println!("   性能提升: 一次打开文件搜索所有关键字，避免重复解压");

            // 处理结果文件
            if should_compress && !all_results.is_empty() {
                match create_zip_archive(directory, &all_results, search_keywords) {
                    Ok(zip_path) => {
                        println!("📦 结果已压缩到: {}", zip_path);

                        // 删除原始 JSON 文件
                        for file_path in &all_results {
                            let _ = std::fs::remove_file(file_path);
                        }
                        println!("🧹 已清理 {} 个临时 JSON 文件", all_results.len());
                    }
                    Err(e) => {
                        eprintln!("❌ 创建 ZIP 文件失败: {}", e);
                    }
                }
            } else if !all_results.is_empty() {
                println!("📄 生成了 {} 个 JSON 结果文件", all_results.len());
            } else {
                println!("📄 没有找到匹配的结果");
            }
        }
        Err(e) => {
            eprintln!("❌ 多关键字搜索失败: {}", e);
        }
    }
}

/// 收集多关键字搜索的结果文件
fn collect_multi_keyword_result_files(directory: &str, _search_keywords: &[String]) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let mut result_files = Vec::new();

    for entry in std::fs::read_dir(directory)? {
        let entry = entry?;
        let path = entry.path();

        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            // 查找所有JSON结果文件（文件名与tgz文件名相同）
            if file_name.ends_with(".json") && !file_name.starts_with("ck_search_results_") {
                result_files.push(path.to_string_lossy().to_string());
            }
        }
    }

    Ok(result_files)
}

/// 收集指定关键字的结果文件（保留用于兼容性）
fn collect_result_files(directory: &str, keyword: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let mut result_files = Vec::new();

    for entry in std::fs::read_dir(directory)? {
        let entry = entry?;
        let path = entry.path();

        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            if file_name.ends_with("_search_results.json") {
                // 检查文件内容是否包含当前关键字的搜索结果
                if let Ok(content) = std::fs::read_to_string(&path) {
                    if content.contains(keyword) {
                        result_files.push(path.to_string_lossy().to_string());
                    }
                }
            }
        }
    }

    Ok(result_files)
}

/// 创建 ZIP 压缩包
fn create_zip_archive(
    directory: &str,
    result_files: &[String],
    search_keywords: &[String],
) -> Result<String, Box<dyn std::error::Error>> {
    use std::io::Write;

    // 生成 ZIP 文件名 - 固定格式：ck_search_results_时间戳.zip
    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
    let zip_filename = format!("ck_search_results_{}.zip", timestamp);
    let zip_path = Path::new(directory).join(&zip_filename);

    // 创建 ZIP 文件
    let zip_file = std::fs::File::create(&zip_path)?;
    let mut zip = zip::ZipWriter::new(zip_file);

    let options = zip::write::FileOptions::default()
        .compression_method(zip::CompressionMethod::Deflated)
        .unix_permissions(0o755);

    // 添加每个结果文件到 ZIP
    for file_path in result_files {
        let file_name = Path::new(file_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown.json");

        zip.start_file(file_name, options)?;

        let content = std::fs::read(file_path)?;
        zip.write_all(&content)?;
    }

    // 创建搜索摘要文件
    let summary = create_search_summary(search_keywords, result_files.len());
    zip.start_file("search_summary.txt", options)?;
    zip.write_all(summary.as_bytes())?;

    zip.finish()?;

    Ok(zip_path.to_string_lossy().to_string())
}

/// 创建搜索摘要
fn create_search_summary(search_keywords: &[String], file_count: usize) -> String {
    let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC");

    format!(
        "MMKV 搜索结果摘要\n\
        ==================\n\
        \n\
        搜索时间: {}\n\
        搜索关键字: {}\n\
        结果文件数: {}\n\
        \n\
        关键字详情:\n\
        {}\n\
        \n\
        说明:\n\
        - 每个 JSON 文件包含对应 tgz 文件的搜索结果\n\
        - 文件名格式: <tgz文件名>_search_results.json\n\
        - 搜索结果包含匹配的 key/value 及其详细信息\n",
        timestamp,
        search_keywords.join(", "),
        file_count,
        search_keywords.iter()
            .enumerate()
            .map(|(i, keyword)| format!("  {}. {}", i + 1, keyword))
            .collect::<Vec<_>>()
            .join("\n")
    )
}

